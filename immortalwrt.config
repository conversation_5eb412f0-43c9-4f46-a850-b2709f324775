# ==========================================================
# TARGET_DEVICE
# ==========================================================
CONFIG_DEFAULT_default-settings-chn=y
CONFIG_TARGET_mediatek=y
CONFIG_TARGET_mediatek_filogic_DEVICE_jdcloud_re-cp-03=y
CONFIG_TARGET_mediatek_mt7986=y
CONFIG_TARGET_ARCH_PACKAGES="aarch64_cortex-a53"
CONFIG_TARGET_PER_DEVICE_ROOTFS=y
CONFIG_TARGET_PROFILE="DEVICE_jdcloud_re-cp-03"
CONFIG_TARGET_OPTIMIZATION="-O2 -pipe -mcpu=cortex-a53"

# ==========================================================
# DEV_TOOLS
# ==========================================================
CONFIG_DEVEL=y
CONFIG_INCLUDE_CONFIG=y
CONFIG_JSON_OVERVIEW_IMAGE_INFO=y
CONFIG_TOOLCHAINOPTS=y

# ==========================================================
# SECURITY
# ==========================================================
CONFIG_AFALG_UPDATE_CTR_IV=y
CONFIG_AFALG_FALLBACK=n
CONFIG_OPENSSL_WITH_NPN=y
CONFIG_OPENSSL_ENGINE_BUILTIN=n
CONFIG_OPENSSL_PREFER_CHACHA_OVER_GCM=n
CONFIG_PKG_CHECK_FORMAT_SECURITY=n
CONFIG_PKG_FORTIFY_SOURCE_1=n
CONFIG_PKG_FORTIFY_SOURCE_2=y
CONFIG_WOLFSSL_HAS_ECC25519=n

# ==========================================================
# KERNEL
# ==========================================================
CONFIG_KERNEL_CFS_BANDWIDTH=y
CONFIG_KERNEL_CGROUP_DEVICE=y
CONFIG_KERNEL_CGROUP_FREEZER=y
CONFIG_KERNEL_DEBUG_INFO=n
CONFIG_KERNEL_DEBUG_INFO_REDUCED=n
CONFIG_KERNEL_DEVMEM=y
CONFIG_KERNEL_NET_CLS_CGROUP=y
CONFIG_KERNEL_PERF_EVENTS=y
CONFIG_KERNEL_BLK_DEV_THROTTLING=n
CONFIG_KERNEL_KEYS=n
CONFIG_KERNEL_MEMCG_SWAP=n

# ==========================================================
# BusyBox
# ==========================================================
CONFIG_BUSYBOX_CONFIG_BLKID=y
CONFIG_BUSYBOX_CONFIG_FEATURE_BLKID_TYPE=y
CONFIG_BUSYBOX_CONFIG_VOLUMEID=y
CONFIG_BUSYBOX_CUSTOM=y

# ==========================================================
# MediaTek_MT7986_WiFi
# ==========================================================
CONFIG_CONNINFRA_AUTO_UP=y
CONFIG_CONNINFRA_EMI_SUPPORT=y
CONFIG_MTK_ACK_CTS_TIMEOUT_SUPPORT=y
CONFIG_MTK_AIR_MONITOR=y
CONFIG_MTK_AMPDU_CONF_SUPPORT=y
CONFIG_MTK_ANTENNA_CONTROL_SUPPORT=y
CONFIG_MTK_APCLI_SUPPORT=y
CONFIG_MTK_ATE_SUPPORT=y
CONFIG_MTK_BACKGROUND_SCAN_SUPPORT=y
CONFIG_MTK_BAND_STEERING=n
CONFIG_MTK_CAL_BIN_FILE_SUPPORT=y
CONFIG_MTK_CFG_SUPPORT_FALCON_MURU=y
CONFIG_MTK_CFG_SUPPORT_FALCON_PP=y
CONFIG_MTK_CFG_SUPPORT_FALCON_SR=y
CONFIG_MTK_CFG_SUPPORT_FALCON_TXCMD_DBG=y
CONFIG_MTK_CHIP_MT7986=y
CONFIG_MTK_CONNINFRA_APSOC=y
CONFIG_MTK_CONNINFRA_APSOC_MT7986=y
CONFIG_MTK_CON_WPS_SUPPORT=y
CONFIG_MTK_DBDC_MODE=y
CONFIG_MTK_DEFAULT_5G_PROFILE=n
CONFIG_MTK_DOT11_HE_AX=y
CONFIG_MTK_DOT11_N_SUPPORT=y
CONFIG_MTK_DOT11_VHT_AC=y
CONFIG_MTK_DOT11K_RRM_SUPPORT=y
CONFIG_MTK_DOT11R_FT_SUPPORT=y
CONFIG_MTK_DOT11W_PMF_SUPPORT=y
CONFIG_MTK_FAST_NAT_SUPPORT=y
CONFIG_MTK_FIRST_IF_EEPROM_FLASH=y
CONFIG_MTK_FIRST_IF_IPAILNA=y
CONFIG_MTK_FIRST_IF_MT7986=y
CONFIG_MTK_G_BAND_256QAM_SUPPORT=y
CONFIG_MTK_GREENAP_SUPPORT=y
CONFIG_MTK_HDR_TRANS_RX_SUPPORT=y
CONFIG_MTK_HDR_TRANS_TX_SUPPORT=y
CONFIG_MTK_ICAP_SUPPORT=y
CONFIG_MTK_IGMP_SNOOP_SUPPORT=y
CONFIG_MTK_INTERWORKING=y
CONFIG_MTK_MAC_REPEATER_SUPPORT=n
CONFIG_MTK_MAP_R2_6E_SUPPORT=y
CONFIG_MTK_MAP_R2_VER_SUPPORT=y
CONFIG_MTK_MAP_R3_6E_SUPPORT=y
CONFIG_MTK_MAP_R3_VER_SUPPORT=y
CONFIG_MTK_MAP_SUPPORT=y
CONFIG_MTK_MBO_SUPPORT=y
CONFIG_MTK_MBSS_DTIM_SUPPORT=y
CONFIG_MTK_MBSS_SUPPORT=y
CONFIG_MTK_MCAST_RATE_SPECIFIC=y
CONFIG_MTK_MGMT_TXPWR_CTRL=y
CONFIG_MTK_MLME_MULTI_QUEUE_SUPPORT=y
CONFIG_MTK_MT_AP_SUPPORT=m
CONFIG_MTK_MT_DFS_SUPPORT=y
CONFIG_MTK_MT_MAC=y
CONFIG_MTK_MT_WIFI=m
CONFIG_MTK_MT_WIFI_PATH="mt_wifi"
CONFIG_MTK_MUMIMO_SUPPORT=y
CONFIG_MTK_MU_RA_SUPPORT=y
CONFIG_MTK_OFFCHANNEL_SCAN_FEATURE=y
CONFIG_MTK_OWE_SUPPORT=y
CONFIG_MTK_PCIE_ASPM_DYM_CTRL_SUPPORT=n
CONFIG_MTK_PHY_ICS_SUPPORT=y
CONFIG_MTK_PRE_CAL_TRX_SET1_SUPPORT=n
CONFIG_MTK_PRE_CAL_TRX_SET2_SUPPORT=n
CONFIG_MTK_QOS_R1_SUPPORT=y
CONFIG_MTK_RA_PHY_RATE_SUPPORT=y
CONFIG_MTK_RED_SUPPORT=y
CONFIG_MTK_RLM_CAL_CACHE_SUPPORT=n
CONFIG_MTK_RTMP_FLASH_SUPPORT=y
CONFIG_MTK_RT_FIRST_CARD_EEPROM="flash"
CONFIG_MTK_RT_FIRST_IF_RF_OFFSET=0xc0000
CONFIG_MTK_SCS_FW_OFFLOAD=y
CONFIG_MTK_SECOND_IF_NONE=y
CONFIG_MTK_SNIFFER_RADIOTAP_SUPPORT=n
CONFIG_MTK_SMART_CARRIER_SENSE_SUPPORT=y
CONFIG_MTK_SPECTRUM_SUPPORT=y
CONFIG_MTK_SUPPORT_OPENWRT=y
CONFIG_MTK_THERMAL_PROTECT_SUPPORT=y
CONFIG_MTK_THIRD_IF_NONE=y
CONFIG_MTK_TPC_SUPPORT=y
CONFIG_MTK_TXBF_SUPPORT=y
CONFIG_MTK_UAPSD=y
CONFIG_MTK_VLAN_SUPPORT=y
CONFIG_MTK_VOW_SUPPORT=y
CONFIG_MTK_WARP_V2=y
CONFIG_MTK_WDS_SUPPORT=y
CONFIG_MTK_WHNAT_SUPPORT=m
CONFIG_MTK_WIFI_ADIE_TYPE="mt7976"
CONFIG_MTK_WIFI_BASIC_FUNC=y
CONFIG_MTK_WIFI_DRIVER=y
CONFIG_MTK_WIFI_EAP_FEATURE=y
CONFIG_MTK_WIFI_FW_BIN_LOAD=y
CONFIG_MTK_WIFI_MODE_AP=m
CONFIG_MTK_WIFI_MT_MAC=y
CONFIG_MTK_WIFI_SKU_TYPE="AX6000"
CONFIG_MTK_WIFI_TWT_SUPPORT=y
CONFIG_MTK_WLAN_HOOK=y
CONFIG_MTK_WLAN_SERVICE=y
CONFIG_MTK_WNM_SUPPORT=y
CONFIG_MTK_WPA3_SUPPORT=y
CONFIG_MTK_WSC_INCLUDED=y
CONFIG_MTK_WSC_V2_SUPPORT=y
CONFIG_WARP_CHIPSET="mt7986"
CONFIG_WARP_DBG_SUPPORT=y
CONFIG_WARP_MEMORY_LEAK_DBG=y
CONFIG_WARP_NEW_FW=y
CONFIG_WARP_VERSION=2
CONFIG_WED_HW_RRO_SUPPORT=y
CONFIG_first_card=y
CONFIG_first_card_name="MT7986"

# ==========================================================
# CONTAINERD
# ==========================================================
CONFIG_DOCKER_CGROUP_OPTIONS=y
CONFIG_DOCKER_NET_MACVLAN=y
CONFIG_DOCKER_NET_OVERLAY=y
CONFIG_DOCKER_STO_BTRFS=y
CONFIG_DOCKER_STO_EXT4=y
CONFIG_PACKAGE_cgroupfs-mount=y
CONFIG_PACKAGE_containerd=y
CONFIG_PACKAGE_docker=y
CONFIG_PACKAGE_docker-compose=y
CONFIG_PACKAGE_dockerd=y
CONFIG_PACKAGE_luci-app-dockerman=y
CONFIG_PACKAGE_podman=y
CONFIG_PACKAGE_runc=y
CONFIG_PACKAGE_tini=y

# ==========================================================
# DISK_TOOLS
# ==========================================================
CONFIG_PACKAGE_automount=y
CONFIG_PACKAGE_blkid=y
CONFIG_PACKAGE_btrfs-progs=y
CONFIG_PACKAGE_cfdisk=y
CONFIG_PACKAGE_e2fsprogs=y
CONFIG_PACKAGE_f2fs-tools=y
CONFIG_PACKAGE_f2fsck=y
CONFIG_PACKAGE_gdisk=y
CONFIG_PACKAGE_kmod-dm=y
CONFIG_PACKAGE_kmod-fs-ext4=y
CONFIG_PACKAGE_kmod-fs-f2fs=y
CONFIG_PACKAGE_kmod-fuse=y
CONFIG_PACKAGE_kmod-usb-storage=y
CONFIG_PACKAGE_lsof=y
CONFIG_PACKAGE_mkf2fs=y
CONFIG_PACKAGE_mmc-utils=y
CONFIG_PACKAGE_mount-utils=y
CONFIG_PACKAGE_parted=y
CONFIG_PACKAGE_swap-utils=y
CONFIG_PACKAGE_ubi-utils=y
CONFIG_PARTED_READLINE=y
CONFIG_PACKAGE_luci-app-diskman=y
CONFIG_PACKAGE_block-mount=y
CONFIG_PACKAGE_blockd=y
CONFIG_PACKAGE_kmod-fs-autofs4=y
CONFIG_PACKAGE_kmod-fs-vfat=y
CONFIG_PACKAGE_kmod-nls-base=y
CONFIG_PACKAGE_kmod-nls-cp437=y
CONFIG_PACKAGE_kmod-nls-iso8859-1=y
CONFIG_PACKAGE_kmod-nls-utf8=y
CONFIG_PACKAGE_kmod-scsi-core=y
CONFIG_PACKAGE_kmod-ata-core=y
CONFIG_PACKAGE_sgdisk=y

# ==========================================================
# NETWORK
# ==========================================================
# DDNS
CONFIG_PACKAGE_ddns-scripts=y
CONFIG_PACKAGE_ddns-scripts-aliyun=y
CONFIG_PACKAGE_ddns-scripts-cloudflare=y
CONFIG_PACKAGE_ddns-scripts-cnkuai=y
CONFIG_PACKAGE_ddns-scripts-digitalocean=y
CONFIG_PACKAGE_ddns-scripts-dnspod=y
CONFIG_PACKAGE_ddns-scripts-dnspod-com=y
CONFIG_PACKAGE_ddns-scripts-freedns=y
CONFIG_PACKAGE_ddns-scripts-gandi=y
CONFIG_PACKAGE_ddns-scripts-godaddy=y
CONFIG_PACKAGE_ddns-scripts-noip=y
CONFIG_PACKAGE_ddns-scripts-nsupdate=y
CONFIG_PACKAGE_ddns-scripts-route53=y
CONFIG_PACKAGE_luci-app-ddns=y

# VPN
CONFIG_PACKAGE_luci-app-ipsec-vpnserver-manyusers=y
CONFIG_PACKAGE_luci-app-openvpn-server=y
CONFIG_PACKAGE_luci-app-passwall=y
CONFIG_PACKAGE_luci-app-wireguard=y
CONFIG_PACKAGE_luci-app-zerotier=y
CONFIG_PACKAGE_tailscale=y
CONFIG_PACKAGE_luci-app-vlmcsd=y
CONFIG_PACKAGE_luci-app-adguardhome=y
CONFIG_PACKAGE_luci-app-banip=y

# NET_MANAGER
CONFIG_PACKAGE_luci-app-arpbind=y
CONFIG_PACKAGE_luci-app-nlbwmon=y
CONFIG_PACKAGE_luci-app-turboacc-mtk=y
CONFIG_PACKAGE_luci-app-upnp=y
CONFIG_PACKAGE_luci-app-wol=y
CONFIG_PACKAGE_luci-app-msd_lite=y
CONFIG_PACKAGE_luci-app-eqos-mtk=y
CONFIG_PACKAGE_luci-app-lucky=y
CONFIG_PACKAGE_luci-app-mtwifi-cfg=y
CONFIG_PACKAGE_etherwake=y
CONFIG_PACKAGE_luci-app-wolplus=y
CONFIG_PACKAGE_luci-app-netdata=y
CONFIG_PACKAGE_luci-app-sqm=y
CONFIG_PACKAGE_sqm-scripts=y
CONFIG_PACKAGE_luci-app-ntpc=y

# NET_TOOLS
CONFIG_PACKAGE_iperf3=y
CONFIG_PACKAGE_tcpdump=y
CONFIG_PACKAGE_tcping=y
CONFIG_PACKAGE_mtr=y
CONFIG_PACKAGE_ipt2socks=y
CONFIG_PACKAGE_odhcp6c=y
CONFIG_PACKAGE_odhcp6c_ext_cer_id=0
CONFIG_PACKAGE_odhcpd-ipv6only=y
CONFIG_PACKAGE_odhcpd_ipv6only_ext_cer_id=0
CONFIG_PACKAGE_ppp=y
CONFIG_PACKAGE_ppp-mod-pppoe=y
CONFIG_PACKAGE_tc-mod-iptables=y
CONFIG_PACKAGE_tc-tiny=y
CONFIG_PACKAGE_uclient-fetch=y
CONFIG_PACKAGE_resolveip=y

# ==========================================================
# FIREWALL
# ==========================================================
CONFIG_PACKAGE_ebtables=y
CONFIG_PACKAGE_ip-bridge=y
CONFIG_PACKAGE_ip6tables-extra=y
CONFIG_PACKAGE_ip6tables-nft=y
CONFIG_PACKAGE_ipset=y
CONFIG_PACKAGE_iptables-mod-conntrack-extra=y
CONFIG_PACKAGE_iptables-mod-extra=y
CONFIG_PACKAGE_iptables-mod-filter=y
CONFIG_PACKAGE_iptables-mod-hashlimit=y
CONFIG_PACKAGE_iptables-mod-iface=y
CONFIG_PACKAGE_iptables-mod-ipmark=y
CONFIG_PACKAGE_iptables-mod-ipopt=y
CONFIG_PACKAGE_iptables-mod-iprange=y
CONFIG_PACKAGE_iptables-mod-ipv4options=y
CONFIG_PACKAGE_iptables-mod-nat-extra=y
CONFIG_PACKAGE_iptables-mod-proto=y
CONFIG_PACKAGE_iptables-mod-tee=y
CONFIG_PACKAGE_iptables-mod-tproxy=y
CONFIG_PACKAGE_iptables-mod-u32=y
CONFIG_PACKAGE_kmod-br-netfilter=y
CONFIG_PACKAGE_kmod-ebtables=y
CONFIG_PACKAGE_kmod-ebtables-ipv4=y
CONFIG_PACKAGE_kmod-ebtables-ipv6=y
CONFIG_PACKAGE_kmod-ifb=y
CONFIG_PACKAGE_kmod-ip6tables-extra=y
CONFIG_PACKAGE_kmod-ipt-compat-xtables=y
CONFIG_PACKAGE_kmod-ipt-conntrack-extra=y
CONFIG_PACKAGE_kmod-ipt-extra=y
CONFIG_PACKAGE_kmod-ipt-filter=y
CONFIG_PACKAGE_kmod-ipt-hashlimit=y
CONFIG_PACKAGE_kmod-ipt-iface=y
CONFIG_PACKAGE_kmod-ipt-ipmark=y
CONFIG_PACKAGE_kmod-ipt-ipopt=y
CONFIG_PACKAGE_kmod-ipt-iprange=y
CONFIG_PACKAGE_kmod-ipt-ipv4options=y
CONFIG_PACKAGE_kmod-ipt-nat-extra=y
CONFIG_PACKAGE_kmod-ipt-offload=y
CONFIG_PACKAGE_kmod-ipt-proto=y
CONFIG_PACKAGE_kmod-ipt-raw6=y
CONFIG_PACKAGE_kmod-ipt-tee=y
CONFIG_PACKAGE_kmod-ipt-tproxy=y
CONFIG_PACKAGE_kmod-ipt-u32=y
CONFIG_PACKAGE_kmod-nf-flow=y
CONFIG_PACKAGE_kmod-nf-ipvs=y
CONFIG_PACKAGE_kmod-veth=y
CONFIG_PACKAGE_luci-app-firewall=y

# ==========================================================
# Liber
# ==========================================================
CONFIG_PACKAGE_base-files=y
CONFIG_PACKAGE_ca-certificates=y
CONFIG_PACKAGE_coremark=y
CONFIG_PACKAGE_datconf=y
CONFIG_PACKAGE_datconf-lua=y
CONFIG_PACKAGE_ethtool=y
CONFIG_PACKAGE_htop=y
CONFIG_PACKAGE_kmod-conninfra=y
CONFIG_PACKAGE_kmod-crypto-acompress=y
CONFIG_PACKAGE_kmod-crypto-ccm=y
CONFIG_PACKAGE_kmod-crypto-cmac=y
CONFIG_PACKAGE_kmod-crypto-crc32c=y
CONFIG_PACKAGE_kmod-crypto-ctr=y
CONFIG_PACKAGE_kmod-crypto-des=y
CONFIG_PACKAGE_kmod-crypto-gcm=y
CONFIG_PACKAGE_kmod-crypto-gf128=y
CONFIG_PACKAGE_kmod-crypto-ghash=y
CONFIG_PACKAGE_kmod-crypto-hmac=y
CONFIG_PACKAGE_kmod-crypto-md4=y
CONFIG_PACKAGE_kmod-crypto-md5=y
CONFIG_PACKAGE_kmod-crypto-rng=y
CONFIG_PACKAGE_kmod-crypto-seqiv=y
CONFIG_PACKAGE_kmod-crypto-sha256=y
CONFIG_PACKAGE_kmod-crypto-sha512=y
CONFIG_PACKAGE_kmod-inet-diag=y
CONFIG_PACKAGE_kmod-lib-crc32c=y
CONFIG_PACKAGE_kmod-lib-lzo=y
CONFIG_PACKAGE_kmod-mediatek_hnat=y
CONFIG_PACKAGE_kmod-mt_wifi=y
CONFIG_PACKAGE_kmod-sched-core=y
CONFIG_PACKAGE_kmod-tcp-bbr=y
CONFIG_PACKAGE_kmod-tun=y
CONFIG_PACKAGE_kmod-warp=y
CONFIG_PACKAGE_kmod-zram=y
CONFIG_PACKAGE_kvcedit=y
CONFIG_PACKAGE_libatomic=y
CONFIG_PACKAGE_libblkid=y
CONFIG_PACKAGE_libcap-ng=y
CONFIG_PACKAGE_libcbor=y
CONFIG_PACKAGE_libevdev=y
CONFIG_PACKAGE_libfido2=y
CONFIG_PACKAGE_libipset=y
CONFIG_PACKAGE_libkvcutil=y
CONFIG_PACKAGE_libncurses=y
CONFIG_PACKAGE_libnl=y
CONFIG_PACKAGE_libnl-core=y
CONFIG_PACKAGE_libnl-genl=y
CONFIG_PACKAGE_libnl-nf=y
CONFIG_PACKAGE_libnl-route=y
CONFIG_PACKAGE_libopenssl-afalg_sync=y
CONFIG_PACKAGE_libopenssl-devcrypto=y
CONFIG_PACKAGE_libpcap=y
CONFIG_PACKAGE_libstdcpp=y
CONFIG_PACKAGE_libudev-zero=y
CONFIG_PACKAGE_libuuid=y
CONFIG_PACKAGE_luci-app-ttyd=y
CONFIG_PACKAGE_luci-i18n-mtwifi-cfg-zh-cn=y
CONFIG_PACKAGE_luci-i18n-turboacc-mtk-zh-cn=y
CONFIG_PACKAGE_mii_mgr=y
CONFIG_PACKAGE_mtk-smp=y
CONFIG_PACKAGE_mtkhqos_util=y
CONFIG_PACKAGE_nano=y
CONFIG_PACKAGE_openssh-keygen=y
CONFIG_PACKAGE_openssh-sftp-server=y
CONFIG_PACKAGE_openssl-util=y
CONFIG_PACKAGE_qrencode=y
CONFIG_PACKAGE_regs=y
CONFIG_PACKAGE_switch=y
CONFIG_PACKAGE_terminfo=y
CONFIG_PACKAGE_ttyd=y
CONFIG_PACKAGE_vim-fuller=y
CONFIG_PACKAGE_wifi-dats=y
CONFIG_PACKAGE_wifi-scripts=y
CONFIG_PACKAGE_wireless-regdb=y
CONFIG_PACKAGE_wireless-tools=y
CONFIG_PACKAGE_zram-swap=y
CONFIG_PACKAGE_zsh=y
CONFIG_PACKAGE_luci-app-statistics=y
CONFIG_PACKAGE_collectd-mod-cpu=y
CONFIG_PACKAGE_collectd-mod-memory=y
CONFIG_PACKAGE_collectd-mod-interface=y
CONFIG_PACKAGE_luci-app-commands=y
CONFIG_PACKAGE_luci-app-crontab=y
CONFIG_PACKAGE_luci-app-log=y
CONFIG_PACKAGE_luci-app-advanced-reboot=y

# ==========================================================
# LuCI_THEME
# ==========================================================
CONFIG_PACKAGE_luci-theme-argon=y
CONFIG_PACKAGE_luci-theme-bootstrap-mod=y
CONFIG_PACKAGE_luci-theme-design=y




CONFIG_TARGET_mediatek=y
CONFIG_TARGET_mediatek_mt7986=y
CONFIG_TARGET_mediatek_filogic_DEVICE_jdcloud_re-cp-03=y
CONFIG_TARGET_ARCH_PACKAGES="aarch64_cortex-a53"
CONFIG_TARGET_PROFILE="DEVICE_jdcloud_re-cp-03"

CONFIG_DEVEL=y
CONFIG_TOOLCHAINOPTS=y
CONFIG_BUSYBOX_CUSTOM=y
CONFIG_TARGET_PER_DEVICE_ROOTFS=y
CONFIG_AFALG_UPDATE_CTR_IV=y
CONFIG_BUSYBOX_CONFIG_BLKID=y
CONFIG_BUSYBOX_CONFIG_FEATURE_BLKID_TYPE=y
CONFIG_BUSYBOX_CONFIG_VOLUMEID=y
CONFIG_CONNINFRA_AUTO_UP=y
CONFIG_CONNINFRA_EMI_SUPPORT=y
CONFIG_INCLUDE_CONFIG=y
CONFIG_JSON_OVERVIEW_IMAGE_INFO=y
CONFIG_KERNEL_CGROUP_DEVICE=y
CONFIG_KERNEL_CGROUP_FREEZER=y
CONFIG_KERNEL_DEVMEM=y
CONFIG_KERNEL_NET_CLS_CGROUP=y
CONFIG_MTK_ACK_CTS_TIMEOUT_SUPPORT=y
CONFIG_MTK_AIR_MONITOR=y
CONFIG_MTK_AMPDU_CONF_SUPPORT=y
CONFIG_MTK_ANTENNA_CONTROL_SUPPORT=y
CONFIG_MTK_APCLI_SUPPORT=y
CONFIG_MTK_ATE_SUPPORT=y
CONFIG_MTK_BACKGROUND_SCAN_SUPPORT=y
CONFIG_MTK_CAL_BIN_FILE_SUPPORT=y
CONFIG_MTK_CFG_SUPPORT_FALCON_MURU=y
CONFIG_MTK_CFG_SUPPORT_FALCON_PP=y
CONFIG_MTK_CFG_SUPPORT_FALCON_SR=y
CONFIG_MTK_CFG_SUPPORT_FALCON_TXCMD_DBG=y
CONFIG_MTK_CHIP_MT7986=y
CONFIG_MTK_CONNINFRA_APSOC=y
CONFIG_MTK_CONNINFRA_APSOC_MT7986=y
CONFIG_MTK_CON_WPS_SUPPORT=y
CONFIG_MTK_DBDC_MODE=y
CONFIG_MTK_DOT11K_RRM_SUPPORT=y
CONFIG_MTK_DOT11R_FT_SUPPORT=y
CONFIG_MTK_DOT11W_PMF_SUPPORT=y
CONFIG_MTK_DOT11_HE_AX=y
CONFIG_MTK_DOT11_N_SUPPORT=y
CONFIG_MTK_DOT11_VHT_AC=y
CONFIG_MTK_FAST_NAT_SUPPORT=y
CONFIG_MTK_FIRST_IF_EEPROM_FLASH=y
CONFIG_MTK_FIRST_IF_IPAILNA=y
CONFIG_MTK_FIRST_IF_MT7986=y
CONFIG_MTK_GREENAP_SUPPORT=y
CONFIG_MTK_G_BAND_256QAM_SUPPORT=y
CONFIG_MTK_HDR_TRANS_RX_SUPPORT=y
CONFIG_MTK_HDR_TRANS_TX_SUPPORT=y
CONFIG_MTK_ICAP_SUPPORT=y
CONFIG_MTK_IGMP_SNOOP_SUPPORT=y
CONFIG_MTK_INTERWORKING=y
CONFIG_MTK_BAND_STEERING=y
CONFIG_MTK_MAP_SUPPORT=y
CONFIG_MTK_MAP_R2_VER_SUPPORT=y
CONFIG_MTK_MAP_R3_VER_SUPPORT=y
CONFIG_MTK_MAP_R2_6E_SUPPORT=y
CONFIG_MTK_MAP_R3_6E_SUPPORT=y
CONFIG_MTK_MBO_SUPPORT=y
CONFIG_MTK_MBSS_DTIM_SUPPORT=y
CONFIG_MTK_MBSS_SUPPORT=y
CONFIG_MTK_MCAST_RATE_SPECIFIC=y
CONFIG_MTK_MGMT_TXPWR_CTRL=y
CONFIG_MTK_MLME_MULTI_QUEUE_SUPPORT=y
CONFIG_MTK_MT7986_NEW_FW=y
CONFIG_MTK_MT_AP_SUPPORT=m
CONFIG_MTK_MT_DFS_SUPPORT=y
CONFIG_MTK_MT_MAC=y
CONFIG_MTK_MT_WIFI=m
CONFIG_MTK_MT_WIFI_PATH="mt_wifi"
CONFIG_MTK_MUMIMO_SUPPORT=y
CONFIG_MTK_MU_RA_SUPPORT=y
CONFIG_MTK_OFFCHANNEL_SCAN_FEATURE=y
CONFIG_MTK_OWE_SUPPORT=y
CONFIG_MTK_PHY_ICS_SUPPORT=y
CONFIG_MTK_QOS_R1_SUPPORT=y
CONFIG_MTK_RA_PHY_RATE_SUPPORT=y
CONFIG_MTK_RED_SUPPORT=y
CONFIG_MTK_RTMP_FLASH_SUPPORT=y
CONFIG_MTK_RT_FIRST_CARD_EEPROM="flash"
CONFIG_MTK_RT_FIRST_IF_RF_OFFSET=0xc0000
CONFIG_MTK_SCS_FW_OFFLOAD=y
CONFIG_MTK_SECOND_IF_NONE=y
CONFIG_MTK_SMART_CARRIER_SENSE_SUPPORT=y
CONFIG_MTK_SPECTRUM_SUPPORT=y
CONFIG_MTK_SUPPORT_OPENWRT=y
CONFIG_MTK_THERMAL_PROTECT_SUPPORT=y
CONFIG_MTK_THIRD_IF_NONE=y
CONFIG_MTK_TPC_SUPPORT=y
CONFIG_MTK_TXBF_SUPPORT=y
CONFIG_MTK_UAPSD=y
CONFIG_MTK_VLAN_SUPPORT=y
CONFIG_MTK_VOW_SUPPORT=y
CONFIG_MTK_WARP_V2=y
CONFIG_MTK_WDS_SUPPORT=y
CONFIG_MTK_WHNAT_SUPPORT=m
CONFIG_MTK_WIFI_ADIE_TYPE="mt7976"
CONFIG_MTK_WIFI_BASIC_FUNC=y
CONFIG_MTK_WIFI_DRIVER=y
CONFIG_MTK_WIFI_EAP_FEATURE=y
CONFIG_MTK_WIFI_FW_BIN_LOAD=y
CONFIG_MTK_WIFI_MODE_AP=m
CONFIG_MTK_WIFI_MT_MAC=y
CONFIG_MTK_WIFI_SKU_TYPE="AX6000"
CONFIG_MTK_WIFI_TWT_SUPPORT=y
CONFIG_MTK_WLAN_HOOK=y
CONFIG_MTK_WLAN_SERVICE=y
CONFIG_MTK_WNM_SUPPORT=y
CONFIG_MTK_WPA3_SUPPORT=y
CONFIG_MTK_WSC_INCLUDED=y
CONFIG_MTK_WSC_V2_SUPPORT=y
CONFIG_OPENSSL_WITH_NPN=y
CONFIG_PACKAGE_TURBOACC_INCLUDE_NO_FASTPATH=y
CONFIG_PACKAGE_blockd=y
CONFIG_PACKAGE_ca-certificates=y
CONFIG_PACKAGE_datconf=y
CONFIG_PACKAGE_datconf-lua=y
CONFIG_PACKAGE_ethtool=y
CONFIG_PACKAGE_ebtables=y
CONFIG_PACKAGE_htop=y
CONFIG_PACKAGE_ip-bridge=y
CONFIG_PACKAGE_ip6tables-extra=y
CONFIG_PACKAGE_ip6tables-nft=y
CONFIG_PACKAGE_ipset=y
CONFIG_PACKAGE_iptables-mod-conntrack-extra=y
CONFIG_PACKAGE_iptables-mod-extra=y
CONFIG_PACKAGE_iptables-mod-filter=y
CONFIG_PACKAGE_iptables-mod-hashlimit=y
CONFIG_PACKAGE_iptables-mod-iface=y
CONFIG_PACKAGE_iptables-mod-ipmark=y
CONFIG_PACKAGE_iptables-mod-ipopt=y
CONFIG_PACKAGE_iptables-mod-iprange=y
CONFIG_PACKAGE_iptables-mod-ipv4options=y
CONFIG_PACKAGE_iptables-mod-nat-extra=y
CONFIG_PACKAGE_iptables-mod-proto=y
CONFIG_PACKAGE_iptables-mod-tee=y
CONFIG_PACKAGE_iptables-mod-tproxy=y
CONFIG_PACKAGE_iptables-mod-u32=y
CONFIG_PACKAGE_iw=y
CONFIG_PACKAGE_iwinfo=y
CONFIG_PACKAGE_kmod-ata-core=y
CONFIG_PACKAGE_kmod-conninfra=y
CONFIG_PACKAGE_kmod-crypto-acompress=y
CONFIG_PACKAGE_kmod-crypto-ccm=y
CONFIG_PACKAGE_kmod-crypto-cmac=y
CONFIG_PACKAGE_kmod-crypto-crc32c=y
CONFIG_PACKAGE_kmod-crypto-ctr=y
CONFIG_PACKAGE_kmod-crypto-des=y
CONFIG_PACKAGE_kmod-crypto-gcm=y
CONFIG_PACKAGE_kmod-crypto-gf128=y
CONFIG_PACKAGE_kmod-crypto-ghash=y
CONFIG_PACKAGE_kmod-crypto-hmac=y
CONFIG_PACKAGE_kmod-crypto-md4=y
CONFIG_PACKAGE_kmod-crypto-md5=y
CONFIG_PACKAGE_kmod-crypto-rng=y
CONFIG_PACKAGE_kmod-crypto-seqiv=y
CONFIG_PACKAGE_kmod-crypto-sha256=y
CONFIG_PACKAGE_kmod-crypto-sha512=y
CONFIG_PACKAGE_kmod-ebtables=y
CONFIG_PACKAGE_kmod-ebtables-ipv4=y
CONFIG_PACKAGE_kmod-ebtables-ipv6=y
CONFIG_PACKAGE_kmod-fs-autofs4=y
CONFIG_PACKAGE_kmod-fs-vfat=y
CONFIG_PACKAGE_kmod-inet-diag=y
CONFIG_PACKAGE_kmod-ip6tables-extra=y
CONFIG_PACKAGE_kmod-ipt-compat-xtables=y
CONFIG_PACKAGE_kmod-ipt-conntrack-extra=y
CONFIG_PACKAGE_kmod-ipt-extra=y
CONFIG_PACKAGE_kmod-ipt-filter=y
CONFIG_PACKAGE_kmod-ipt-hashlimit=y
CONFIG_PACKAGE_kmod-ipt-iface=y
CONFIG_PACKAGE_kmod-ipt-ipmark=y
CONFIG_PACKAGE_kmod-ipt-ipopt=y
CONFIG_PACKAGE_kmod-ipt-iprange=y
CONFIG_PACKAGE_kmod-ipt-ipv4options=y
CONFIG_PACKAGE_kmod-ipt-nat-extra=y
CONFIG_PACKAGE_kmod-ipt-offload=y
CONFIG_PACKAGE_kmod-ipt-proto=y
CONFIG_PACKAGE_kmod-ipt-raw6=y
CONFIG_PACKAGE_kmod-ipt-tee=y
CONFIG_PACKAGE_kmod-ipt-tproxy=y
CONFIG_PACKAGE_kmod-ipt-u32=y
CONFIG_PACKAGE_kmod-leds-ws2812b=y
CONFIG_PACKAGE_kmod-lib-crc32c=y
CONFIG_PACKAGE_kmod-lib-lzo=y
CONFIG_PACKAGE_kmod-mediatek_hnat=y
CONFIG_PACKAGE_kmod-mt_wifi=y
CONFIG_PACKAGE_kmod-nf-flow=y
CONFIG_PACKAGE_kmod-nls-base=y
CONFIG_PACKAGE_kmod-nls-cp437=y
CONFIG_PACKAGE_kmod-nls-iso8859-1=y
CONFIG_PACKAGE_kmod-nls-utf8=y
CONFIG_PACKAGE_kmod-scsi-core=y
CONFIG_PACKAGE_kmod-tcp-bbr=y
CONFIG_PACKAGE_kmod-tun=y
CONFIG_PACKAGE_kmod-warp=y
CONFIG_PACKAGE_kmod-zram=y
CONFIG_PACKAGE_kvcedit=y
CONFIG_PACKAGE_libatomic=y
CONFIG_PACKAGE_libblkid=y
CONFIG_PACKAGE_libcap-ng=y
CONFIG_PACKAGE_libcbor=y
CONFIG_PACKAGE_libevdev=y
CONFIG_PACKAGE_libfido2=y
CONFIG_PACKAGE_libipset=y
CONFIG_PACKAGE_libkvcutil=y
CONFIG_PACKAGE_libncurses=y
CONFIG_PACKAGE_libnl=y
CONFIG_PACKAGE_libnl-core=y
CONFIG_PACKAGE_libnl-genl=y
CONFIG_PACKAGE_libnl-nf=y
CONFIG_PACKAGE_libnl-route=y
CONFIG_PACKAGE_libopenssl-afalg_sync=y
CONFIG_PACKAGE_libopenssl-devcrypto=y
CONFIG_PACKAGE_libpcap=y
CONFIG_PACKAGE_libstdcpp=y
CONFIG_PACKAGE_libudev-zero=y
CONFIG_PACKAGE_libuuid=y
CONFIG_PACKAGE_luci-app-eqos-mtk=y
CONFIG_PACKAGE_luci-app-mtwifi-cfg=y
CONFIG_PACKAGE_lua-cjson=y
CONFIG_PACKAGE_luci-app-ssr-plus_INCLUDE_NONE_V2RAY=y
CONFIG_PACKAGE_wifi-scripts=y
CONFIG_PACKAGE_luci-app-ssr-plus_INCLUDE_Shadowsocks_NONE_Client=y
CONFIG_PACKAGE_luci-app-ssr-plus_INCLUDE_Shadowsocks_NONE_Server=y
CONFIG_PACKAGE_luci-app-turboacc-mtk=y
CONFIG_PACKAGE_luci-app-wrtbwmon=y
CONFIG_PACKAGE_luci-i18n-mtwifi-cfg-zh-cn=y
CONFIG_PACKAGE_luci-i18n-turboacc-mtk-zh-cn=y
CONFIG_PACKAGE_luci-theme-argon=y
CONFIG_PACKAGE_luci-theme-bootstrap-mod=y
CONFIG_PACKAGE_mii_mgr=y
CONFIG_PACKAGE_mtkhqos_util=y
CONFIG_PACKAGE_mtk-smp=y
CONFIG_PACKAGE_nano=y
CONFIG_PACKAGE_openssh-keygen=y
CONFIG_PACKAGE_openssh-sftp-server=y
CONFIG_PACKAGE_openssl-util=y
CONFIG_PACKAGE_regs=y
CONFIG_PACKAGE_resolveip=y
CONFIG_PACKAGE_switch=y
CONFIG_PACKAGE_tcpdump=y
CONFIG_PACKAGE_terminfo=y
CONFIG_PACKAGE_wifi-dats=y
CONFIG_PACKAGE_mtwifi-cfg=y
CONFIG_PACKAGE_wireless-regdb=y
CONFIG_PACKAGE_wireless-tools=y
CONFIG_PACKAGE_zram-swap=y
CONFIG_PKG_FORTIFY_SOURCE_2=y
CONFIG_WARP_CHIPSET="mt7986"
CONFIG_WARP_DBG_SUPPORT=y
CONFIG_WARP_MEMORY_LEAK_DBG=y
CONFIG_WARP_NEW_FW=y
CONFIG_WARP_VERSION=2
CONFIG_WED_HW_RRO_SUPPORT=y
CONFIG_first_card=y
CONFIG_first_card_name="MT7986"
