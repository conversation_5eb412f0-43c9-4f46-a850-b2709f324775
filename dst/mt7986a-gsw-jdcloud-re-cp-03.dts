// SPDX-License-Identifier: GPL-2.0-or-later OR MIT

/dts-v1/;
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/leds/common.h>

#include "mt7986a.dtsi"
#include "mt7986a-pinctrl.dtsi"

/ {
	model = "JDCloud RE-CP-03";
	compatible = "jdcloud,re-cp-03", "mediatek,mt7986a";

	aliases {
		led-boot = &red_led;
		led-failsafe = &red_led;
		led-running = &green_led;
		led-upgrade = &green_led;
	};

	chosen {
		bootargs = "console=ttyS0,115200n1 loglevel=8  \
				earlycon=uart8250,mmio32,0x11002000 \
				root=PARTLABEL=rootfs rootwait rootfstype=squashfs,f2fs";
	};

	memory {
		reg = <0 0x40000000 0 0x40000000>;
	};

	gsw: gsw@0 {
		compatible = "mediatek,mt753x";
		mediatek,ethsys = <&ethsys>;
		#address-cells = <1>;
		#size-cells = <0>;
	};

	gpio-keys {
		compatible = "gpio-keys";

		button-joylink {
			label = "joylink";
			linux,code = <BTN_0>;
			gpios = <&pio 10 GPIO_ACTIVE_LOW>;
		};

		button-reset {
			label = "reset";
			linux,code = <KEY_RESTART>;
			gpios = <&pio 9 GPIO_ACTIVE_LOW>;
		};
	};

	gpio-leds {
		compatible = "gpio-leds";

		led-0 {
			label = "blue:status";
			gpios = <&pio 7 GPIO_ACTIVE_HIGH>;
		};

		red_led: led-1 {
			label = "red:status";
			gpios = <&pio 11 GPIO_ACTIVE_HIGH>;
		};

		green_led: led-2 {
			label = "green:status";
			gpios = <&pio 12 GPIO_ACTIVE_LOW>;
		};
	};

	reg_1p8v: regulator-1p8v {
		compatible = "regulator-fixed";
		regulator-name = "fixed-1.8V";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-boot-on;
		regulator-always-on;
	};

	reg_3p3v: regulator-3p3v {
		compatible = "regulator-fixed";
		regulator-name = "fixed-3.3V";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
		regulator-always-on;
	};
};

&eth {
	status = "okay";

	gmac0: mac@0 {
		compatible = "mediatek,eth-mac";
		reg = <0>;
		phy-mode = "2500base-x";

		fixed-link {
			speed = <2500>;
			full-duplex;
			pause;
		};
	};

	gmac1: mac@1 {
		compatible = "mediatek,eth-mac";
		reg = <1>;
		phy-mode = "2500base-x";

		ext-phy-reg = <6>;
		ext-phy-reset-gpios = <&pio 6 0>;

		fixed-link {
			speed = <2500>;
			full-duplex;
			pause;
		};
	};

	mdio: mdio-bus {
		#address-cells = <1>;
		#size-cells = <0>;

		phy5: phy@5 {
			compatible = "ethernet-phy-id67c9.de0a";
			reg = <5>;
		};

		phy6: phy@6 {
			compatible = "ethernet-phy-ieee802.3-c45";
			reg = <6>;
		};
	};
};

&hnat {
	mtketh-wan = "eth1";
	mtketh-lan = "eth0";
	mtketh-max-gmac = <2>;
	status = "okay";
};

&gsw {
	mediatek,mdio = <&mdio>;
	mediatek,portmap = "lllll";
	mediatek,mdio_master_pinmux = <0>;
	reset-gpios = <&pio 5 0>;
	interrupt-parent = <&pio>;
	interrupts = <66 IRQ_TYPE_LEVEL_HIGH>;
	status = "okay";

	port5: port@5 {
		compatible = "mediatek,mt753x-port";
		reg = <5>;
		phy-mode = "sgmii";

		fixed-link {
			speed = <2500>;
			full-duplex;
		};
	};

	port6: port@6 {
		compatible = "mediatek,mt753x-port";
		mediatek,ssc-on;
		reg = <6>;
		phy-mode = "sgmii";
		fixed-link {
			speed = <2500>;
			full-duplex;
		};
	};
};

&mmc0 {
	bus-width = <8>;
	cap-mmc-highspeed;
	hs400-ds-delay = <0x14014>;
	max-frequency = <200000000>;
	mmc-hs200-1_8v;
	mmc-hs400-1_8v;
	no-sd;
	no-sdio;
	non-removable;
	pinctrl-names = "default", "state_uhs";
	pinctrl-0 = <&mmc0_pins_default>;
	pinctrl-1 = <&mmc0_pins_uhs>;
	vmmc-supply = <&reg_3p3v>;
	vqmmc-supply = <&reg_1p8v>;
	status = "okay";
};

&pio {
	mmc0_pins_default: mmc0-pins-default {
		mux {
			function = "flash";
			groups = "emmc_51";
		};
		conf-cmd-dat {
			pins = "EMMC_DATA_0", "EMMC_DATA_1", "EMMC_DATA_2",
			       "EMMC_DATA_3", "EMMC_DATA_4", "EMMC_DATA_5",
			       "EMMC_DATA_6", "EMMC_DATA_7", "EMMC_CMD";
			input-enable;
			drive-strength = <MTK_DRIVE_4mA>;
			mediatek,pull-up-adv = <1>;
		};
		conf-clk {
			pins = "EMMC_CK";
			drive-strength = <MTK_DRIVE_6mA>;
			mediatek,pull-down-adv = <2>;
		};
		conf-ds {
			pins = "EMMC_DSL";
			mediatek,pull-down-adv = <2>;
		};
		conf-rst {
			pins = "EMMC_RSTB";
			drive-strength = <MTK_DRIVE_4mA>;
			mediatek,pull-up-adv = <1>;
		};
	};

	mmc0_pins_uhs: mmc0-uhs-pins {
		mux {
			function = "flash";
			groups = "emmc_51";
		};
		conf-cmd-dat {
			pins = "EMMC_DATA_0", "EMMC_DATA_1", "EMMC_DATA_2",
			       "EMMC_DATA_3", "EMMC_DATA_4", "EMMC_DATA_5",
			       "EMMC_DATA_6", "EMMC_DATA_7", "EMMC_CMD";
			input-enable;
			drive-strength = <MTK_DRIVE_4mA>;
			mediatek,pull-up-adv = <1>;
		};
		conf-clk {
			pins = "EMMC_CK";
			drive-strength = <MTK_DRIVE_6mA>;
			mediatek,pull-down-adv = <2>;
		};
		conf-ds {
			pins = "EMMC_DSL";
			mediatek,pull-down-adv = <2>;
		};
		conf-rst {
			pins = "EMMC_RSTB";
			drive-strength = <MTK_DRIVE_4mA>;
			mediatek,pull-up-adv = <1>;
		};
	};

	wf_2g_5g_pins: wf-2g-5g-pins {
		mux {
			function = "wifi";
			groups = "wf_2g", "wf_5g";
		};
		conf {
			pins = "WF0_HB1", "WF0_HB2", "WF0_HB3", "WF0_HB4",
			       "WF0_HB0", "WF0_HB0_B", "WF0_HB5", "WF0_HB6",
			       "WF0_HB7", "WF0_HB8", "WF0_HB9", "WF0_HB10",
			       "WF0_TOP_CLK", "WF0_TOP_DATA", "WF1_HB1",
			       "WF1_HB2", "WF1_HB3", "WF1_HB4", "WF1_HB0",
			       "WF1_HB5", "WF1_HB6", "WF1_HB7", "WF1_HB8",
			       "WF1_TOP_CLK", "WF1_TOP_DATA";
			drive-strength = <MTK_DRIVE_4mA>;
		};
	};
};

&uart0 {
	status = "okay";
};

&watchdog {
	status = "okay";
};

&wbsys {
	pinctrl-names = "default";
	pinctrl-0 = <&wf_2g_5g_pins>;
	status = "okay";
};