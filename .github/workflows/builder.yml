#
# Copyright (c) 2019-2020 P3TERX <https://p3terx.com>
#
# This is free software, licensed under the MIT License.
# See /LICENSE for more information.
#
# https://github.com/P3TERX/Actions-OpenWrt
# Description: Build OpenWrt using GitHub Actions
#
###############手动修改##############
name: jdcloud_re-cp-03-immortalwrt-24.10

on:
  repository_dispatch:
  workflow_dispatch:
    inputs:
      LAN_IP:
        description: 'Set LAN IP Address'
        required: true
        default: '***********'
      NO_DOCKERMAN:
        description: 'Not build luci-app-dockerman'
        required: true
        default: false
        type: boolean
      ssh:
        description: 'SSH connection to Actions'
        required: false
        default: 'false'
#  schedule:
#    - cron: 0 16 * * *

env:
  ###############手动修改##############
  OPENWRT_NAME: immortalwrt
  REPO_URL: https://github.com/padavanonly/immortalwrt-mt798x-6.6
  REPO_BRANCH: openwrt-24.10-6.6
  FEEDS_CONF: feeds.conf.default
  CONFIG_FILE: immortalwrt.config
  DIY_P1_SH: diy-part1.sh
  DIY_P2_SH: diy-part2.sh
  SSH_ACTIONS: false
  UPLOAD_FIRMWARE: true
  UPLOAD_RELEASE: true
  TZ: Asia/Shanghai

jobs:
  build:
    runs-on: ubuntu-22.04

    permissions:
      contents: write

    steps:
    - name: 检查项目分支
      uses: actions/checkout@main

    - name: 初始化编译环境
      env:
        DEBIAN_FRONTEND: noninteractive
      run: |
        sudo rm -rf /etc/apt/sources.list.d/* /usr/share/dotnet /usr/local/lib/android /opt/ghc
        sudo -E apt-get -qq update
        sudo -E apt-get -qq install $(curl -fsSL git.io/depends-ubuntu-2004)
        sudo -E apt-get -qq autoremove --purge
        sudo -E apt-get -qq clean
        sudo timedatectl set-timezone "$TZ"
        sudo mkdir -p /workdir
        sudo chown $USER:$GROUPS /workdir

    - name: 清理磁盘空间(Ubuntu)
      uses: jlumbroso/free-disk-space@main
      with:
        # this might remove tools that are actually needed,
        # when set to "true" but frees about 6 GB
        tool-cache: true
        
        # all of these default to true, but feel free to set to
        # "false" if necessary for your workflow
        android: true
        dotnet: true
        haskell: true
        large-packages: true
        swap-storage: true

    - name: 下载固件源码
      working-directory: /workdir
      run: |
        df -hT $PWD
        git clone $REPO_URL -b $REPO_BRANCH openwrt
        ln -sf /workdir/openwrt $GITHUB_WORKSPACE/openwrt

    - name: 加载feeds.conf.default & DIY_P1_SH
      run: |
        [ -e $FEEDS_CONF ] && mv $FEEDS_CONF openwrt/feeds.conf.default
        chmod +x $DIY_P1_SH
        cd openwrt
        $GITHUB_WORKSPACE/$DIY_P1_SH

    - name: 更新 & 安装 feeds
      run: |
        cd openwrt
        ./scripts/feeds update -a
        ./scripts/feeds install -a

    - name: 加载config & DIY_P2_SH
      run: |
        [ -e $CONFIG_FILE ] && mv $CONFIG_FILE openwrt/.config
        chmod +x $DIY_P2_SH
        cd openwrt
        $GITHUB_WORKSPACE/$DIY_P2_SH

    - name: 设置LAN IP地址（路由器登录地址）
      run: |
        cd openwrt
        SET_IP=${{ github.event.inputs.LAN_IP }}
        if [[ $SET_IP =~ ^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$ ]]; then
            #修改immortalwrt.lan关联IP
            sed -i "s/192\.168\.[0-9]*\.[0-9]*/$SET_IP/g" $(find feeds/luci/modules/luci-mod-system -type f -name "flash.js")
            #修改默认IP地址
            sed -i "s/192\.168\.[0-9]*\.[0-9]*/$SET_IP/g" package/base-files/files/bin/config_generate
            echo "Set LAN IP Address: $SET_IP"
        else
            echo "Invalid IP address, use default."
        fi

    - name: 取消编译dockerman
      if: github.event.inputs.NO_DOCKERMAN == 'true'
      run: |
        cd openwrt
        sed -i '/CONFIG_PACKAGE_luci-app-dockerman=y/d' .config

    - name: SSH链接管理
      uses: P3TERX/ssh2actions@v1.0.0
      if: (github.event.inputs.ssh == 'true' && github.event.inputs.ssh  != 'false') || contains(github.event.action, 'ssh')
      env:
        TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}
        TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}

    - name: 下载安装包
      id: package
      run: |
        cd openwrt
        make defconfig
        make download -j$(nproc)
        find dl -size -1024c -exec ls -l {} \;
        find dl -size -1024c -exec rm -f {} \;

    - name: 编译固件
      id: compile
      run: |
        cd openwrt
        echo -e "$(nproc) thread compile"
        make -j$(nproc) V=s || make -j1 V=s
        grep '^CONFIG_TARGET.*DEVICE.*=y' .config | sed -r 's/.*DEVICE_(.*)=y/\1/' > DEVICE_NAME
        [ -s DEVICE_NAME ] && echo "DEVICE_NAME=$(cat DEVICE_NAME)" >> $GITHUB_ENV
        grep '^CONFIG_TARGET.*DEVICE.*=y' .config | sed -r 's/.*TARGET_.*_(.*)_DEVICE_.*=y/\1/' > TARGET_NAME
        [ -s TARGET_NAME ] && echo "TARGET_NAME=$(cat TARGET_NAME)" >> $GITHUB_ENV
        sed -n 's/.*lan) ipad=${ipaddr:-"\([0-9]\+\.[0-9]\+\.[0-9]\+\.[0-9]\+\)"} ;;.*/\1/p' package/base-files/files/bin/config_generate | head -n 1 > IP_ADDR
        [ -s IP_ADDR ] && echo "IP_ADDR=$(cat IP_ADDR)" >> $GITHUB_ENV
        if grep -q 'CONFIG_PACKAGE_luci-app-dockerman=y' .config; then
                echo "BUILD_DOCKERMAN=-docker" >> $GITHUB_ENV
        else
                echo "BUILD_DOCKERMAN=" >> $GITHUB_ENV
        fi
        echo "FILE_DATE=$(date +"%Y.%m.%d-%H%M")" >> $GITHUB_ENV
        echo "status=success" >> $GITHUB_OUTPUT

    - name: 查看磁盘使用情况
      if: (!cancelled())
      run: df -hT

    - name: 整理文件并重命名
      id: organize
      if: env.UPLOAD_FIRMWARE == 'true' && !cancelled()
      run: | 
        cd openwrt/bin/targets/*/*
        rm -rf packages
        find ./ -iregex ".*\(bl31-uboot.fip\|gpt.bin\|preloader.bin\|bl2.bin\)$" -exec rm -rf {} +
        sudo -E apt-get -qq install rename
        rename 's/.*${{ env.DEVICE_NAME }}/${{ env.FILE_DATE }}-${{ env.OPENWRT_NAME }}-${{ env.DEVICE_NAME }}${{ env.BUILD_DOCKERMAN }}/' *
        echo "FIRMWARE=$PWD" >> $GITHUB_ENV
        echo "status=success" >> $GITHUB_OUTPUT

    - name: 打包上传固件到Actions Artifacts
      uses: actions/upload-artifact@main
      if: steps.organize.outputs.status == 'success' && !cancelled()
      with:
        name: ${{ env.FILE_DATE }}-${{ env.OPENWRT_NAME }}-${{ env.DEVICE_NAME }}${{ env.BUILD_DOCKERMAN }}
        path: ${{ env.FIRMWARE }}

    - name: 生成固件Release标签
      id: tag
      if: env.UPLOAD_RELEASE == 'true' && !cancelled()
      run: |
        touch release.txt
        echo "- 使用源码：${{ env.REPO_URL }}" >> release.txt
        echo "- 使用分支：${{ env.REPO_BRANCH }}" >> release.txt
        echo "- 登录地址：${{ env.IP_ADDR }}" >> release.txt
        if [[ "${{ env.BUILD_DOCKERMAN }}" == '-docker' ]]; then
                echo "- 已编译luci-app-dockerman" >> release.txt
        else
                echo "- 未编译luci-app-dockerman" >> release.txt
        fi
        echo "release_tag=${{ env.FILE_DATE }}-${{ env.OPENWRT_NAME }}-${{ env.DEVICE_NAME }}${{ env.BUILD_DOCKERMAN }}" >> $GITHUB_OUTPUT
        echo "status=success" >> $GITHUB_OUTPUT

    - name: 发布固件至Release
      uses: softprops/action-gh-release@v2.1.0
      if: steps.tag.outputs.status == 'success' && !cancelled()
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        files: ${{ env.FIRMWARE }}/*
        name: ${{ env.FILE_DATE }}-${{ env.OPENWRT_NAME }}-${{ env.DEVICE_NAME }}${{ env.BUILD_DOCKERMAN }}
        tag_name: ${{ steps.tag.outputs.release_tag }}
        body_path: release.txt
